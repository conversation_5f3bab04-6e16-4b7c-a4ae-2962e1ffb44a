export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      email_logs: {
        Row: {
          certificate_id: string | null
          created_at: string | null
          email_type: string
          error_message: string | null
          id: string
          recipient_email: string
          resend_message_id: string | null
          sent_at: string | null
          status: string
          updated_at: string | null
        }
        Insert: {
          certificate_id?: string | null
          created_at?: string | null
          email_type: string
          error_message?: string | null
          id?: string
          recipient_email: string
          resend_message_id?: string | null
          sent_at?: string | null
          status?: string
          updated_at?: string | null
        }
        Update: {
          certificate_id?: string | null
          created_at?: string | null
          email_type?: string
          error_message?: string | null
          id?: string
          recipient_email?: string
          resend_message_id?: string | null
          sent_at?: string | null
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "email_logs_certificate_id_fkey"
            columns: ["certificate_id"]
            isOneToOne: false
            referencedRelation: "energieausweise"
            referencedColumns: ["id"]
          }
        ]
      }
      payment_attempts: {
        Row: {
          abandonment_reason: string | null
          amount_cents: number | null
          attempt_status: string
          certificate_id: string | null
          completed_at: string | null
          created_at: string | null
          currency: string | null
          failure_reason: string | null
          id: string
          ip_address: string | null
          payment_method: string | null
          session_duration_seconds: number | null
          stripe_payment_intent_id: string | null
          stripe_session_id: string | null
          updated_at: string | null
          user_agent: string | null
        }
        Insert: {
          abandonment_reason?: string | null
          amount_cents?: number | null
          attempt_status?: string
          certificate_id?: string | null
          completed_at?: string | null
          created_at?: string | null
          currency?: string | null
          failure_reason?: string | null
          id?: string
          ip_address?: string | null
          payment_method?: string | null
          session_duration_seconds?: number | null
          stripe_payment_intent_id?: string | null
          stripe_session_id?: string | null
          updated_at?: string | null
          user_agent?: string | null
        }
        Update: {
          abandonment_reason?: string | null
          amount_cents?: number | null
          attempt_status?: string
          certificate_id?: string | null
          completed_at?: string | null
          created_at?: string | null
          currency?: string | null
          failure_reason?: string | null
          id?: string
          ip_address?: string | null
          payment_method?: string | null
          session_duration_seconds?: number | null
          stripe_payment_intent_id?: string | null
          stripe_session_id?: string | null
          updated_at?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payment_attempts_certificate_id_fkey"
            columns: ["certificate_id"]
            isOneToOne: false
            referencedRelation: "energieausweise"
            referencedColumns: ["id"]
          }
        ]
      }
      stripe_webhook_events: {
        Row: {
          certificate_extraction_method: string | null
          certificate_id: string | null
          created_at: string | null
          error_message: string | null
          event_created_at: string
          event_type: string
          id: string
          processing_status: string
          raw_event_data: Json
          stripe_event_id: string
          updated_at: string | null
        }
        Insert: {
          certificate_extraction_method?: string | null
          certificate_id?: string | null
          created_at?: string | null
          error_message?: string | null
          event_created_at: string
          event_type: string
          id?: string
          processing_status?: string
          raw_event_data: Json
          stripe_event_id: string
          updated_at?: string | null
        }
        Update: {
          certificate_extraction_method?: string | null
          certificate_id?: string | null
          created_at?: string | null
          error_message?: string | null
          event_created_at?: string
          event_type?: string
          id?: string
          processing_status?: string
          raw_event_data?: Json
          stripe_event_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "stripe_webhook_events_certificate_id_fkey"
            columns: ["certificate_id"]
            isOneToOne: false
            referencedRelation: "energieausweise"
            referencedColumns: ["id"]
          }
        ]
      }
      energieausweise: {
        Row: {
          certificate_type: string | null
          created_at: string | null
          fenster: Json | null
          gebaeudedetails1: Json | null
          gebaeudedetails2: Json | null
          heizung: Json | null
          id: string
          lueftung: Json | null
          objektdaten: Json | null
          order_number: string | null
          payment_status: string | null
          status: string | null
          stripe_checkout_session_id: string | null
          trinkwarmwasser: Json | null
          updated_at: string | null
          user_id: string | null
          verbrauchsdaten: Json | null
        }
        Insert: {
          certificate_type?: string | null
          created_at?: string | null
          fenster?: Json | null
          gebaeudedetails1?: Json | null
          gebaeudedetails2?: Json | null
          heizung?: Json | null
          id?: string
          lueftung?: Json | null
          objektdaten?: Json | null
          order_number?: string | null
          payment_status?: string | null
          status?: string | null
          stripe_checkout_session_id?: string | null
          trinkwarmwasser?: Json | null
          updated_at?: string | null
          user_id?: string | null
          verbrauchsdaten?: Json | null
        }
        Update: {
          certificate_type?: string | null
          created_at?: string | null
          fenster?: Json | null
          gebaeudedetails1?: Json | null
          gebaeudedetails2?: Json | null
          heizung?: Json | null
          id?: string
          lueftung?: Json | null
          objektdaten?: Json | null
          order_number?: string | null
          payment_status?: string | null
          status?: string | null
          stripe_checkout_session_id?: string | null
          trinkwarmwasser?: Json | null
          updated_at?: string | null
          user_id?: string | null
          verbrauchsdaten?: Json | null
        }
        Relationships: []
      }
      payments: {
        Row: {
          amount: number
          created_at: string | null
          currency: string
          customer_id: string | null
          id: string
          order_number: string | null
          payment_intent_id: string | null
          payment_method: string
          status: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          currency?: string
          customer_id?: string | null
          id?: string
          order_number?: string | null
          payment_intent_id?: string | null
          payment_method: string
          status: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          currency?: string
          customer_id?: string | null
          id?: string
          order_number?: string | null
          payment_intent_id?: string | null
          payment_method?: string
          status?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          building_image: string | null
          created_at: string | null
          form_data: Json | null
          form_locked: boolean
          full_name: string | null
          id: string
          invoice_files: Json | null
          password_hash: string | null
          role: string
          updated_at: string | null
        }
        Insert: {
          building_image?: string | null
          created_at?: string | null
          form_data?: Json | null
          form_locked?: boolean
          full_name?: string | null
          id: string
          invoice_files?: Json | null
          password_hash?: string | null
          role?: string
          updated_at?: string | null
        }
        Update: {
          building_image?: string | null
          created_at?: string | null
          form_data?: Json | null
          form_locked?: boolean
          full_name?: string | null
          id?: string
          invoice_files?: Json | null
          password_hash?: string | null
          role?: string
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_email: {
        Args: { user_id: string }
        Returns: string
      }
      get_user_name: {
        Args: { user_id: string }
        Returns: string
      }
      is_admin_user: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      transfer_certificate_ownership: {
        Args: { certificate_id: string; new_user_id: string }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const